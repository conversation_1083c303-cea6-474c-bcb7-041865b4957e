// 产品相关 API

import request from '../utils/request'
import type { ProductCategoryResponse, ProductSearchListResponse } from './types/product'

/**
 * 产品相关 API
 */
export const productApi = {
  /**
   * 获取带分类分组的产品信息
   * @description 获取所有分类及其下的产品信息，按分类分组返回
   * @returns Promise<ProductCategoryResponse[]> 分类及产品信息列表
   */
  getProductCategories(): Promise<ProductCategoryResponse[]> {
    return request.get('/products/categories')
  },

  /**
   * 搜索产品
   * @description 根据关键词模糊搜索产品名称，返回匹配的产品列表
   * @param keyword 搜索关键词
   * @returns Promise<ProductSearchListResponse> 搜索结果列表
   */
  searchProducts(keyword: string): Promise<ProductSearchListResponse> {
    return request.get('/products/search', { keyword }, { skipAuth: true })
  }
}
