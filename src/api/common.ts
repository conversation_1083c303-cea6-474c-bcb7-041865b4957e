import request from '../utils/request'
import type { SystemConfigResponse, HelpListResponse } from './types/common'

// 通用接口 API
export const commonApi = {
  /**
   * 获取系统配置
   * 获取系统配置信息，包括热门搜索等
   */
  getSystemConfig: (): Promise<SystemConfigResponse> => {
    return request.get('/common/system-config', {}, { skipAuth: true })
  },

  /**
   * 获取所有帮助信息
   * 获取所有帮助信息，按排序字段升序排列
   */
  getHelps: (): Promise<HelpListResponse> => {
    return request.get('/common/helps', {}, { skipAuth: true })
  }
}
