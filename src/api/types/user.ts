// 用户相关的数据类型定义

// 用户类型枚举
export enum UserType {
  /** 普通用户 */
  Regular = 1,
  /** 会员 */
  VIP = 2
}

// 用户信息响应
export interface UserResponse {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 微信 OpenID */
  open_id: string
  /** 头像URL */
  avatar: string
  /** 用户类型 1:普通用户 2:会员 */
  user_type: UserType
  /** 余额 */
  balance: number
  /** 创建时间 */
  created_at: string
}

// 修改用户名请求
export interface UpdateUsernameRequest {
  /** 新用户名 */
  username: string
}
