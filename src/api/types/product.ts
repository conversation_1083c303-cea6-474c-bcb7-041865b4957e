// 产品相关的数据类型定义

// 产品摘要信息
export interface ProductSummaryResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 产品图片URL */
  image_url1: string
}

// 产品分类响应
export interface ProductCategoryResponse {
  /** 分类ID */
  id: number
  /** 分类名称 */
  name: string
  /** 该分类下的产品列表 */
  children: ProductSummaryResponse[]
}

// 产品搜索结果项
export interface ProductSearchResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 产品图片URL */
  image_url1: string
  /** 折扣提示 */
  discount_tip: string
}

// 产品搜索结果列表
export interface ProductSearchListResponse {
  /** 搜索结果列表 */
  list: ProductSearchResponse[]
}
