import request from '../utils/request'
import type {
  UserResponse,
  UpdateUsernameRequest
} from './types/user'

// 用户管理相关 API
export const userApi = {
  /**
   * 获取当前登录用户信息
   * 获取当前登录用户的详细信息
   */
  getCurrentUserInfo: (): Promise<UserResponse> => {
    return request.get('/users/info')
  },

  /**
   * 退出登录
   * 用户退出登录，清除登录状态
   */
  logout: (): Promise<void> => {
    return request.post('/users/logout')
  },

  /**
   * 修改用户名
   * 修改当前登录用户的用户名
   */
  updateUsername: (data: UpdateUsernameRequest): Promise<void> => {
    return request.put('/users/username', data)
  },

  /**
   * 修改用户头像
   * 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
   */
  updateAvatar: (file: File): Promise<void> => {
    const formData = new FormData()
    formData.append('file', file)

    return request.put('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}