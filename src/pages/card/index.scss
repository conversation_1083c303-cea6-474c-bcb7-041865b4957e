.card-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 32px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 48px;
}

.title {
  font-size: 48px;
  font-weight: bold;
  color: #333;
}

.card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  color: #666;
  font-size: 32px;
  margin-bottom: 16px;
}

.card-desc {
  font-size: 28px;
  color: #999;
}
