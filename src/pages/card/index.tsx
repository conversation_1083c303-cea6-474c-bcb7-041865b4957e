import { View, Text, ScrollView, Image } from '@tarojs/components'
import { useLoad, useReady, createSelectorQuery } from '@tarojs/taro'
import { useState, useRef, useEffect } from 'react'
import { productApi, ProductCategoryResponse } from '@/api'
import { SearchBar } from '@/components'
import './index.scss'

export default function Card() {
  const [categories, setCategories] = useState<ProductCategoryResponse[]>([])
  const [activeCategory, setActiveCategory] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>('')

  // 用于存储每个分类区域的位置信息
  const [categoryPositions, setCategoryPositions] = useState<{ [key: number]: number }>({})
  const [rightScrollTop, setRightScrollTop] = useState<number>(0)
  const isClickScrolling = useRef<boolean>(false)

  useLoad(() => {
    // 页面加载
  })

  useReady(() => {
    fetchProductCategories()
  })

  // 获取产品分类数据
  const fetchProductCategories = async () => {
    try {
      setLoading(true)
      setError('')
      const data = await productApi.getProductCategories()
      setCategories(data)
      if (data.length > 0) {
        setActiveCategory(data[0].id)
      }
    } catch (error) {
      console.error('获取产品分类失败:', error)
      setError('获取数据失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 计算每个分类区域的位置
  const calculateCategoryPositions = () => {
    if (categories.length === 0) return

    setTimeout(() => {
      const query = createSelectorQuery()
      const positions: { [key: number]: number } = {}

      // 查询每个分类区域的位置
      for (let i = 0; i < categories.length; i++) {
        const category = categories[i]
        query.select('#category-' + category.id).boundingClientRect()
      }

      // 查询右侧容器的信息
      query.select('.right-content').boundingClientRect()
      query.select('.right-content').scrollOffset()

      query.exec((res) => {
        console.log('查询结果:', res)
        if (res.length >= categories.length + 2) {
          const containerRect = res[categories.length]
          const scrollInfo = res[categories.length + 1]

          for (let i = 0; i < categories.length; i++) {
            const category = categories[i]
            const rect = res[i]
            if (rect && containerRect) {
              // 计算相对于容器顶部的位置
              positions[category.id] = rect.top - containerRect.top + (scrollInfo ? scrollInfo.scrollTop : 0)
            }
          }

          setCategoryPositions(positions)
          console.log('分类位置信息:', positions)
        } else {
          console.log('查询结果不完整，重试...')
          // 如果查询失败，1秒后重试
          setTimeout(() => {
            calculateCategoryPositions()
          }, 1000)
        }
      })
    }, 200)
  }

  // 数据加载完成后计算位置
  useEffect(() => {
    if (!loading && categories.length > 0) {
      console.log('开始计算分类位置...')
      // 多次尝试计算位置，确保DOM已完全渲染
      setTimeout(() => {
        calculateCategoryPositions()
      }, 500)
      setTimeout(() => {
        calculateCategoryPositions()
      }, 1000)
      setTimeout(() => {
        calculateCategoryPositions()
      }, 2000)
    }
  }, [loading, categories])

  // 左侧分类点击处理
  const handleCategoryClick = (categoryId: number, index: number) => {
    console.log('点击分类:', categoryId, '目标位置:', categoryPositions[categoryId])
    setActiveCategory(categoryId)
    isClickScrolling.current = true

    // 使用预计算的位置进行滚动
    const targetPosition = categoryPositions[categoryId]
    if (targetPosition !== undefined && targetPosition >= 0) {
      // 使用 scrollTop 属性控制滚动
      setRightScrollTop(targetPosition)
      console.log('执行滚动到位置:', targetPosition)
    } else {
      console.log('未找到目标位置，使用估算位置...')
      // 备用方案：使用估算位置
      const estimatedPosition = index * 500
      setRightScrollTop(estimatedPosition)
      console.log('使用估算位置:', estimatedPosition)

      // 同时重新计算准确位置
      recalculatePositions()
    }

    // 重置点击滚动标志
    setTimeout(() => {
      isClickScrolling.current = false
    }, 800)
  }

  // 右侧滚动处理 - 根据滚动位置更新左侧高亮
  const handleRightScroll = (e: any) => {
    if (isClickScrolling.current) return

    const scrollTop = e.detail.scrollTop

    // 根据滚动位置找到当前应该高亮的分类
    let currentCategoryId = categories.length > 0 ? categories[0].id : 0

    for (let i = 0; i < categories.length; i++) {
      const category = categories[i]
      const position = categoryPositions[category.id]
      if (position !== undefined && scrollTop >= position - 50) {
        currentCategoryId = category.id
      } else {
        break
      }
    }

    if (currentCategoryId !== activeCategory) {
      setActiveCategory(currentCategoryId)
    }
  }



  // 搜索处理 - 移除自定义处理，让SearchBar使用默认跳转
  // const handleSearch = (value: string) => {
  //   // 让SearchBar组件处理跳转到搜索页面
  // }

  // 客服按钮点击处理
  const handleCustomerServiceClick = () => {
    // TODO: 实现客服功能
  }

  // 重新计算位置（当内容变化时调用）
  const recalculatePositions = () => {
    setTimeout(() => {
      calculateCategoryPositions()
    }, 300)
  }

  return (
    <View className='card-page'>
      {/* 搜索栏 */}
      <SearchBar
        placeholder='请输入您想找，想出售的东西'
        onCustomerServiceClick={handleCustomerServiceClick}
      />

      {/* 主体内容 */}
      <View className='main-content'>
        {/* 左侧分类导航 */}
        <ScrollView
          className='left-sidebar'
          scrollY
          scrollWithAnimation={false}
          enableBackToTop={false}
        >
          {categories.map((category, index) => (
            <View
              key={category.id}
              className={`category-item ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => handleCategoryClick(category.id, index)}
            >
              <Text className='category-name'>{category.name}</Text>
            </View>
          ))}
        </ScrollView>

        {/* 右侧产品列表 */}
        <ScrollView
          className='right-content'
          scrollY
          scrollWithAnimation={true}
          enableBackToTop={false}
          onScroll={handleRightScroll}
          scrollTop={rightScrollTop}
        >
          {loading ? (
            <View className='loading'>
              <Text>加载中...</Text>
            </View>
          ) : error ? (
            <View className='error-state'>
              <Text className='error-text'>{error}</Text>
              <View className='retry-btn' onClick={fetchProductCategories}>
                <Text>重试</Text>
              </View>
            </View>
          ) : categories.length === 0 ? (
            <View className='empty-state'>
              <Text className='empty-text'>暂无数据</Text>
            </View>
          ) : (
            categories.map((category) => (
              <View
                key={category.id}
                id={`category-${category.id}`}
                className='category-section'
              >
                <View className='category-title'>
                  <Text>{category.name}</Text>
                </View>
                <View className='products-grid'>
                  {category.children.map((product) => (
                    <View key={product.id} className='product-item'>
                      <View className='product-image'>
                        {product.image_url1 ? (
                          <Image
                            src={product.image_url1}
                            className='product-img'
                            mode='aspectFit'
                            onError={() => {}}
                          />
                        ) : (
                          <View className='product-placeholder'>
                            <Text className='placeholder-text'>🎫</Text>
                          </View>
                        )}
                      </View>
                      <Text className='product-name'>{product.name}</Text>
                    </View>
                  ))}
                </View>
              </View>
            ))
          )}
        </ScrollView>
      </View>
    </View>
  )
}
