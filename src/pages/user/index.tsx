import { View, Text } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import './index.scss'

export default function User() {
  useLoad(() => {
    console.log('User page loaded.')
  })

  return (
    <View className='user-page'>
      <View className='header'>
        <Text className='title'>个人中心</Text>
      </View>

      <View className='card'>
        <Text className='card-content'>这里是个人中心页面</Text>
        <Text className='card-desc'>您可以在这里管理个人信息和设置</Text>
      </View>
    </View>
  )
}
