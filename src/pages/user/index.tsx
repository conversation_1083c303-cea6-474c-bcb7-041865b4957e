import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector } from 'react-redux'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import userIcon1 from '../../assets/images/user_icon1.png'
import userIcon2 from '../../assets/images/user_icon2.png'
import userIcon3 from '../../assets/images/user_icon3.png'
import userIcon5 from '../../assets/images/user_icon5.png'
import userIcon6 from '../../assets/images/user_icon6.png'
import userIcon7 from '../../assets/images/user_icon7.png'
import './index.scss'

export default function User() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)

  useLoad(() => {
    console.log('User page loaded.')
  })

  // 跳转到登录页面
  const navigateToLogin = () => {
    Taro.navigateTo({
      url: '/pages/login/index'
    })
  }

  // 处理菜单项点击
  const handleMenuClick = (item: any) => {
    // 帮助与客服不需要登录，直接跳转
    if (item.icon === 'service') {
      Taro.navigateTo({
        url: '/pages/help/index'
      })
      return
    }

    // 其他功能需要登录
    if (!userInfo) {
      // 未登录用户点击功能菜单，提示登录
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        navigateToLogin()
      }, 1500)
      return
    }

    // 已登录用户的功能处理
    switch (item.icon) {
      case 'record':
        Taro.showToast({ title: '出售记录功能开发中', icon: 'none' })
        break
      case 'withdraw':
        Taro.showToast({ title: '提现记录功能开发中', icon: 'none' })
        break
      case 'settings':
        Taro.showToast({ title: '账号设置功能开发中', icon: 'none' })
        break
      case 'notification':
        Taro.showToast({ title: '消息通知功能开发中', icon: 'none' })
        break
      case 'about':
        Taro.showToast({ title: '关于我们功能开发中', icon: 'none' })
        break
      default:
        break
    }
  }

  // 处理提现按钮点击
  const handleWithdraw = () => {
    if (!userInfo) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        navigateToLogin()
      }, 1500)
      return
    }
    Taro.showToast({ title: '提现功能开发中', icon: 'none' })
  }

  // 第一组功能菜单项
  const menuItems1 = [
    {
      icon: 'record',
      title: '出售记录',
      arrow: true
    },
    {
      icon: 'withdraw',
      title: '提现记录',
      arrow: true
    },
    {
      icon: 'settings',
      title: '账号设置',
      arrow: true
    }
  ]

  // 第二组功能菜单项
  const menuItems2 = [
    {
      icon: 'notification',
      title: '消息通知',
      arrow: true
    },
    {
      icon: 'service',
      title: '帮助与客服',
      arrow: true
    },
    {
      icon: 'about',
      title: '关于我们',
      arrow: true
    }
  ]

  // 渲染图标
  const renderIcon = (iconType: string) => {
    const iconStyle = 'menu-icon-svg'
    switch (iconType) {
      case 'record':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon1}
              mode='aspectFit'
            />
          </View>
        )
      case 'withdraw':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon2}
              mode='aspectFit'
            />
          </View>
        )
      case 'settings':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon3}
              mode='aspectFit'
            />
          </View>
        )
      case 'notification':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon5}
              mode='aspectFit'
            />
          </View>
        )
      case 'service':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon6}
              mode='aspectFit'
            />
          </View>
        )
      case 'about':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon7}
              mode='aspectFit'
            />
          </View>
        )
      default:
        return <View className={iconStyle}></View>
    }
  }

  return (
    <View className='user-page'>
      {/* 顶部用户信息区域 */}
      <View className='user-header'>
        <View className='user-info' onClick={userInfo ? undefined : navigateToLogin}>
          <View className='avatar-container'>
            {userInfo?.avatar ? (
              <Image
                className='avatar'
                src={userInfo.avatar}
                mode='aspectFill'
              />
            ) : (
              <View className='avatar avatar-placeholder'>
                <View className='avatar-icon'></View>
              </View>
            )}
          </View>
          <View className='user-details'>
            {userInfo ? (
              <>
                <Text className='username'>{userInfo.username || '用户'}</Text>
                <Text className='phone'>{userInfo.phone}</Text>
              </>
            ) : (
              <View className='login-prompt'>
                <Text className='login-text'>请先进行登录/注册</Text>
                <Text className='login-desc'>页面专业的卡券管理平台~</Text>
              </View>
            )}
          </View>
          <View className='arrow-right'>›</View>
        </View>
      </View>

      {/* 账户余额卡片 */}
      <View className='balance-card'>
        <View className='balance-content'>
          <Text className='balance-label'>账户余额</Text>
          <Text className='balance-amount'>
            {userInfo ? `${userInfo.balance || 0}` : '--'} 元
          </Text>
        </View>
        <View className='withdraw-btn' onClick={handleWithdraw}>
          <Text className='withdraw-text'>提现</Text>
        </View>
      </View>

      {/* 统计数据区域 */}
      <View className='stats-container'>
        <View className='stat-item'>
          <Text className='stat-label'>推广效益</Text>
          <Text className='stat-value'>--</Text>
        </View>
        <View className='stat-item'>
          <Text className='stat-label'>我的好友</Text>
          <Text className='stat-value'>--</Text>
        </View>
      </View>

      {/* 第一组功能菜单列表 */}
      <View className='menu-list'>
        {menuItems1.map((item, index) => (
          <View key={index} className='menu-item' onClick={() => handleMenuClick(item)}>
            <View className='menu-left'>
              {renderIcon(item.icon)}
              <Text className='menu-title'>{item.title}</Text>
            </View>
            {item.arrow && <View className='menu-arrow'>›</View>}
          </View>
        ))}
      </View>

      {/* 第二组功能菜单列表 */}
      <View className='menu-list menu-list-second'>
        {menuItems2.map((item, index) => (
          <View key={index} className='menu-item' onClick={() => handleMenuClick(item)}>
            <View className='menu-left'>
              {renderIcon(item.icon)}
              <Text className='menu-title'>{item.title}</Text>
            </View>
            {item.arrow && <View className='menu-arrow'>›</View>}
          </View>
        ))}
      </View>
    </View>
  )
}
