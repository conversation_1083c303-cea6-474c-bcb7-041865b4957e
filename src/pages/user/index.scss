.user-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
}

/* 顶部用户信息区域 */
.user-header {
  background: white;
  padding: 40px 32px 32px;
  margin-bottom: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
}

.avatar-container {
  margin-right: 24px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light);
}

.avatar-icon {
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: white;
    border-radius: 50%;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
  }

  &::after {
    content: '';
    position: absolute;
    width: 36px;
    height: 20px;
    background-color: white;
    border-radius: 18px 18px 0 0;
    bottom: 6px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.phone {
  display: block;
  font-size: 28px;
  color: #666;
}

.login-prompt {
  cursor: pointer;
}

.login-text {
  display: block;
  font-size: 40px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.login-desc {
  display: block;
  font-size: 24px;
  color: #999;
}

.arrow-right {
  font-size: 40px;
  color: #ccc;
  position: absolute;
  right: 0;
}

/* 账户余额卡片 */
.balance-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  margin: 0 32px 24px;
  border-radius: 24px;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.balance-content {
  flex: 1;
}

.balance-label {
  display: block;
  color: white;
  font-size: 28px;
  margin-bottom: 20px;
  opacity: 0.9;
}

.balance-amount {
  display: block;
  color: white;
  font-size: 32px;
  font-weight: bold;
}

.withdraw-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 48px;
  padding: 12px 32px;
  cursor: pointer;
}

.withdraw-text {
  color: white;
  font-size: 28px;
  font-weight: 500;
}

/* 统计数据区域 */
.stats-container {
  display: flex;
  margin: 0 32px 32px;
  gap: 24px;
}

.stat-item {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 40px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-label {
  font-size: 28px;
  color: #333;
  font-weight: bold;
}

.stat-value {
  font-size: 40px;
  font-weight: bold;
  color: var(--primary-color);
}

/* 功能菜单列表 */
.menu-list {
  background: white;
  margin: 0 32px;
  border-radius: 16px;
  overflow: hidden;
}

.menu-list-second {
  margin-top: 24px;
}

.menu-list-second {
  margin-top: 24px;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon-svg {
  width: 40px;
  height: 40px;
  margin-right: 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.menu-icon-image {
  width: 100%;
  height: 100%;
}

/* 图标样式 - 现在所有图标都使用真实PNG图片 */

.menu-title {
  font-size: 28px;
  color: #333;
  font-weight: bold;
}

.menu-arrow {
  font-size: 32px;
  color: #ccc;
}
