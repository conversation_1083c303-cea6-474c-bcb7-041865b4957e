.order-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 32px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: #999;
  font-size: 28px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 48px;
}

.title {
  font-size: 48px;
  font-weight: bold;
  color: #333;
}

.card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  color: #666;
  font-size: 32px;
  margin-bottom: 16px;
}

.card-desc {
  font-size: 28px;
  color: #999;
  margin-bottom: 16px;
}

.welcome-text {
  font-size: 28px;
  color: var(--primary-color);
}
