.login-container {
  min-height: 100vh;
  background: linear-gradient(to bottom,
    var(--primary-color) 0%,
    var(--primary-hover) 33.33%,
    #f5f5f5 33.33%,
    #f5f5f5 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;



  // 背景装饰
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 120px;
        height: 120px;
        top: 100px;
        right: -30px;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        top: 180px;
        right: 60px;
      }
    }

    .planet {
      position: absolute;
      width: 150px;
      height: 150px;
      top: 120px;
      right: -20px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);
      border: 2px solid rgba(255, 255, 255, 0.1);

      &::before {
        content: '';
        position: absolute;
        width: 200px;
        height: 2px;
        background: rgba(255, 255, 255, 0.1);
        top: 50%;
        left: -25px;
        transform: translateY(-50%) rotate(-15deg);
        border-radius: 1px;
      }
    }
  }

  // 欢迎文字
  .welcome-text {
    padding: 40px 24px 20px;
    position: absolute;
    top: 1%;
    left: 0;
    right: 0;
    z-index: 5;

    .greeting {
      display: block;
      color: white;
      font-size: 48px;
      font-weight: 300;
      margin-bottom: 8px;
    }

    .subtitle {
      display: block;
      color: white;
      font-size: 48px;
      font-weight: 500;
    }
  }

  // 登录表单
  .login-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 40px 32px;
    margin: 0 4px;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    max-width: 600px;
    width: calc(100% - 8px);

    .form-title {
      text-align: center;
      margin-bottom: 40px;

      text {
        font-size: 36px;
        font-weight: 600;
        color: #333;
      }
    }

    // 输入框组
    .input-group {
      margin-bottom: 32px;

      .input-wrapper {
        display: flex;
        align-items: center;
        background: #f0f0f0;
        border-radius: 12px;
        padding: 28px 20px;
        position: relative;

        .input-icon {
          width: 32px;
          height: 32px;
          margin-right: 16px;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;

          &.phone-icon {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23999' viewBox='0 0 24 24'%3E%3Cpath d='M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z'/%3E%3C/svg%3E");
          }

          &.code-icon {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23999' viewBox='0 0 24 24'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
          }
        }

        .input-field {
          flex: 1;
          font-size: 28px;
          color: #333;
          background: transparent;

          &::placeholder {
            color: #999;
          }
        }

        .get-code-btn {
          margin-left: 16px;

          text {
            color: var(--primary-color);
            font-size: 28px;

            &.disabled {
              color: #999;
            }
          }
        }
      }
    }

    // 登录按钮
    .login-btn {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      border: none;
      border-radius: 12px;
      padding: 20px;
      font-size: 32px;
      font-weight: 600;
      margin: 32px 0 24px;

      &::after {
        border: none;
      }
    }

    // 暂不登录
    .skip-login {
      text-align: center;
      margin-bottom: 40px;

      text {
        color: #999;
        font-size: 28px;
      }
    }

    // 协议同意
    .agreement {
      display: flex;
      align-items: center;
      line-height: 1.4;

      .checkbox-wrapper {
        margin-right: 2px;
        flex-shrink: 0;
        align-self: center;

        .checkbox {
          margin: 0;
          transform: scale(0.6);
        }
      }

      .agreement-text {
        flex: 1;
        font-size: 24px;
        color: #999;
        line-height: 1.5;

        .link {
          color: var(--primary-color);
          text-decoration: underline;
          cursor: pointer;

          &:hover {
            color: var(--primary-hover);
          }

          &:active {
            color: var(--primary-dark);
          }
        }
      }
    }
  }
}
