import { View, Text } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import SearchBar from '@/components/SearchBar'
import './index.scss'

export default function Index () {
  useLoad(() => {
    // 页面加载
  })

  return (
    <View className='index'>
      <View className='header'>
        <SearchBar />
      </View>

      <View className='card'>
        <Text className='card-title'>欢迎使用电子卡回收系统</Text>
        <Text className='card-desc'>这里是首页，您可以通过底部导航栏切换到不同的功能页面。</Text>
      </View>

      <View className='card'>
        <Text className='card-title'>系统状态</Text>
        <Text className='status-item'>✅ 样式配置成功</Text>
        <Text className='status-item'>✅ 图片资源加载正常</Text>
        <Text className='status-item'>✅ 多页面导航配置完成</Text>
        <Text className='status-item'>✅ 搜索功能已集成</Text>
      </View>
    </View>
  )
}
