.index {
  min-height: 100vh;
  background-color: #f0f8ff;
  padding: 32px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 48px;
}

.title {
  font-size: 48px;
  font-weight: bold;
  color: var(--primary-color);
}

.card {
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.card-title {
  color: #333;
  font-weight: 600;
  font-size: 32px;
  margin-bottom: 16px;
}

.card-desc {
  font-size: 28px;
  color: #666;
}

.status-item {
  font-size: 28px;
  color: #52c41a;
  margin-top: 8px;
}