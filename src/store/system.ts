import type { SystemConfigResponse } from '../api/types/auth'
import { commonApi } from '../api/common'
import type { Dispatch } from 'redux'

// Action Types
export const SET_SYSTEM_CONFIG = 'SET_SYSTEM_CONFIG'
export const C<PERSON><PERSON>_SYSTEM_CONFIG = 'CLEAR_SYSTEM_CONFIG'
export const FETCH_SYSTEM_CONFIG_REQUEST = 'FETCH_SYSTEM_CONFIG_REQUEST'
export const FETCH_SYSTEM_CONFIG_SUCCESS = 'FETCH_SYSTEM_CONFIG_SUCCESS'
export const FETCH_SYSTEM_CONFIG_FAILURE = 'FETCH_SYSTEM_CONFIG_FAILURE'

// Action 接口定义
export interface SetSystemConfigAction {
  type: typeof SET_SYSTEM_CONFIG
  payload: SystemConfigResponse
}

export interface ClearSystemConfigAction {
  type: typeof CLEAR_SYSTEM_CONFIG
}

export interface FetchSystemConfigRequestAction {
  type: typeof FETCH_SYSTEM_CONFIG_REQUEST
}

export interface FetchSystemConfigSuccessAction {
  type: typeof FETCH_SYSTEM_CONFIG_SUCCESS
  payload: SystemConfigResponse
}

export interface FetchSystemConfigFailureAction {
  type: typeof FETCH_SYSTEM_CONFIG_FAILURE
  payload: string
}

export type SystemActionTypes =
  | SetSystemConfigAction
  | ClearSystemConfigAction
  | FetchSystemConfigRequestAction
  | FetchSystemConfigSuccessAction
  | FetchSystemConfigFailureAction

// Action Creators
export const setSystemConfig = (config: SystemConfigResponse): SetSystemConfigAction => ({
  type: SET_SYSTEM_CONFIG,
  payload: config
})

export const clearSystemConfig = (): ClearSystemConfigAction => ({
  type: CLEAR_SYSTEM_CONFIG
})

// 异步Action Creators
export const fetchSystemConfigRequest = (): FetchSystemConfigRequestAction => ({
  type: FETCH_SYSTEM_CONFIG_REQUEST
})

export const fetchSystemConfigSuccess = (config: SystemConfigResponse): FetchSystemConfigSuccessAction => ({
  type: FETCH_SYSTEM_CONFIG_SUCCESS,
  payload: config
})

export const fetchSystemConfigFailure = (error: string): FetchSystemConfigFailureAction => ({
  type: FETCH_SYSTEM_CONFIG_FAILURE,
  payload: error
})

/**
 * 获取系统配置信息并持久化到store
 * 这是一个异步action，使用redux-thunk中间件
 */
export const fetchSystemConfig = () => {
  return async (dispatch: Dispatch<SystemActionTypes>) => {
    try {
      // 开始请求
      dispatch(fetchSystemConfigRequest())

      // 调用API获取系统配置
      const systemConfig = await commonApi.getSystemConfig()

      // 请求成功，更新store
      dispatch(fetchSystemConfigSuccess(systemConfig))
      dispatch(setSystemConfig(systemConfig))

      return systemConfig
    } catch (error) {
      // 请求失败，记录错误
      const errorMessage = error instanceof Error ? error.message : '获取系统配置失败'
      dispatch(fetchSystemConfigFailure(errorMessage))

      // 重新抛出错误，让调用方可以处理
      throw error
    }
  }
}

// State 类型定义
export interface SystemState {
  config: SystemConfigResponse | null
  loading: boolean
  error: string | null
}

// 初始状态
const INITIAL_STATE: SystemState = {
  config: null,
  loading: false,
  error: null
}

// Reducer
export default function systemReducer(state = INITIAL_STATE, action: SystemActionTypes): SystemState {
  switch (action.type) {
    case SET_SYSTEM_CONFIG:
      return {
        ...state,
        config: action.payload,
        loading: false,
        error: null
      }

    case CLEAR_SYSTEM_CONFIG:
      return {
        ...state,
        config: null,
        loading: false,
        error: null
      }

    case FETCH_SYSTEM_CONFIG_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      }

    case FETCH_SYSTEM_CONFIG_SUCCESS:
      return {
        ...state,
        config: action.payload,
        loading: false,
        error: null
      }

    case FETCH_SYSTEM_CONFIG_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      }

    default:
      return state
  }
}
