/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  gap: 16px;
  flex-shrink: 0; /* 防止搜索栏被压缩 */
  height: 112px; /* 固定搜索栏高度 */
}

.search-input-wrapper {
  flex: 1;
  background: #f5f5f5;
  border-radius: 24px;
  padding: 20px 20px;
  min-height: 32px;
  display: flex;
  align-items: center;
  position: relative;
}

.search-icon {
  width: 28px;
  height: 28px;
  margin-right: 12px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  font-size: 28px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  padding-right: 80px; /* 为搜索按钮留出空间 */

  // 标准CSS placeholder样式
  &::placeholder {
    font-size: 20px !important;
    color: #999 !important;
  }

  // 兼容不同浏览器的placeholder样式
  &::-webkit-input-placeholder {
    font-size: 20px !important;
    color: #999 !important;
  }

  &::-moz-placeholder {
    font-size: 20px !important;
    color: #999 !important;
  }

  &:-ms-input-placeholder {
    font-size: 20px !important;
    color: #999 !important;
  }
}

.search-btn {
  position: absolute;
  right: 8px;
  background: transparent;
  color: #333;
  padding: 12px 20px;
  font-size: 26px;
  font-weight: bold;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.customer-service {
  width: 72px;
  height: 72px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f0f0f0;
  }
}

.customer-service-icon {
  width: 40px;
  height: 40px;
}
