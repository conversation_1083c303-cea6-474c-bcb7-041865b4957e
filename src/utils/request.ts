import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import Taro from '@tarojs/taro'

// 定义响应数据结构
interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 定义请求配置
interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean // 是否跳过认证
}

class Request {
  private instance: AxiosInstance
  private readonly baseURL: string
  private readonly timeout: number

  constructor() {
    this.baseURL = process.env.NODE_ENV === 'development'
      ? 'http://**************:8084/api' // 开发环境
      : 'https://your-api-domain.com/api' // 生产环境
    this.timeout = 10000

    // 创建 axios 实例
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 设置拦截器
    this.setupInterceptors()
  }

  // 从微信缓存中获取 token
  private getToken(): string | null {
    try {
      return Taro.getStorageSync('token') || null
    } catch (error) {
      console.error('获取 token 失败:', error)
      return null
    }
  }

  // 设置 token 到微信缓存
  private setToken(token: string): void {
    try {
      Taro.setStorageSync('token', token)
    } catch (error) {
      console.error('设置 token 失败:', error)
    }
  }

  // 清除 token
  private clearToken(): void {
    try {
      Taro.removeStorageSync('token')
    } catch (error) {
      console.error('清除 token 失败:', error)
    }
  }

  // 设置请求拦截器
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: any) => {
        // 如果不跳过认证，则添加 token
        if (!config.skipAuth) {
          const token = this.getToken()
          if (token) {
            config.headers.Authorization = `Bearer ${token}`
          }
        }

        console.log('发送请求:', {
          url: config.url,
          method: config.method,
          data: config.data,
          params: config.params
        })

        return config
      },
      (error) => {
        console.error('请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const { data } = response

        console.log('响应数据:', data)

        // 根据后端 API 文档，成功状态码为 0
        if (data.code === 0) {
          return data.data
        } else {
          // 业务错误
          this.handleBusinessError(data.code, data.message)
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      },
      (error) => {
        console.error('响应错误:', error)
        this.handleHttpError(error)
        return Promise.reject(error)
      }
    )
  }

  // 处理业务错误
  private handleBusinessError(code: number, message: string): void {
    // 暂时清空，不处理
  }

  // 处理 HTTP 错误
  private handleHttpError(error: any): void {
    // 暂时清空，不处理
  }

  // GET 请求
  public get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
    return this.instance.get(url, {
      params,
      ...config
    })
  }

  // POST 请求
  public post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  // PUT 请求
  public put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  // DELETE 请求
  public delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  // PATCH 请求
  public patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.patch(url, data, config)
  }

  // 上传文件
  public upload<T = any>(url: string, file: any, config?: RequestConfig): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    return this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  }

  // 设置 token（供登录后调用）
  public setAuthToken(token: string): void {
    this.setToken(token)
  }

  // 清除认证信息（供登出时调用）
  public clearAuth(): void {
    this.clearToken()
  }

  // 获取当前token（供外部调用）
  public getAuthToken(): string | null {
    return this.getToken()
  }
}

// 创建实例并导出
const request = new Request()

export default request
