{"miniprogramRoot": "dist/", "projectname": "recycle", "description": "电子卡回收", "appid": "wx3955714a01f50bf5", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}